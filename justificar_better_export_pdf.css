/* ===================================================================== */
/* SNIPPET CSS PARA BETTER EXPORT PDF - TEXTO JUSTIFICADO              */
/* ===================================================================== */
/*
   Este snippet é específico para o plugin Better Export PDF.

   INSTRUÇÕES DE USO:
   1. Salve este arquivo na pasta .obsidian/snippets/
   2. NÃO ative este snippet nas configurações do Obsidian
   3. No plugin Better Export PDF, ative a opção "Select CSS snippets"
   4. Durante a exportação, selecione este snippet na lista de CSS snippets

   IMPORTANTE: Este snippet deve ser usado APENAS com o Better Export PDF,
   não com a exportação nativa do Obsidian.
*/

/* ===================================================================== */
/* CONFIGURAÇÕES RÁPIDAS - PERSONALIZE AQUI                            */
/* ===================================================================== */
/*
   CORES PREDEFINIDAS - Descomente a linha desejada:

   Creme (sua cor atual):     rgb(253, 246, 227)
   Branco puro:               rgb(255, 255, 255)
   Off-white:                 rgb(250, 250, 250)
   Papel antigo:              rgb(255, 253, 240)
   Bege claro:                rgb(245, 245, 220)
   Marfim:                    rgb(255, 255, 240)

   Para usar uma cor personalizada, altere o valor na seção
   "CONFIGURAÇÕES GERAIS PARA EXPORTAÇÃO PDF" abaixo.
*/

/* ===================================================================== */
/* CONFIGURAÇÕES GERAIS PARA EXPORTAÇÃO PDF                             */
/* ===================================================================== */

/*
   PERSONALIZAÇÃO DE CORES:
   Para alterar a cor de fundo, modifique a variável --pdf-background-color
   Exemplos de cores:
   - Creme: rgb(253, 246, 227)
   - Branco: #ffffff ou rgb(255, 255, 255)
   - Off-white: rgb(250, 250, 250)
   - Papel antigo: rgb(255, 253, 240)
*/

/* Reset de margens e configurações básicas */
@media print {
    :root {
        /* PERSONALIZE AQUI: Altere esta cor para a cor de fundo desejada */
        --pdf-background-color: rgb(253, 246, 227);
        --pdf-text-color: #000;
    }

    * {
        box-sizing: border-box;
    }

    body {
        margin: 0;
        padding: 0;
        line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }

    /* Garante que todos os elementos mantenham a cor de fundo */
    html {
        background: var(--pdf-background-color) !important;
    }

    /* Evita que elementos filhos sobrescrevam o fundo */
    .print-container,
    .export-container,
    .page-content {
        background: var(--pdf-background-color) !important;
    }
}

/* ===================================================================== */
/* JUSTIFICAÇÃO DE TEXTO - CONFIGURAÇÕES PRINCIPAIS                     */
/* ===================================================================== */

@media print {
    /* Parágrafos principais */
    p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
        margin-bottom: 1em !important;
        orphans: 2 !important;
        widows: 2 !important;
    }
    
    /* Conteúdo do markdown */
    .markdown-preview-view p,
    .markdown-reading-view p,
    .markdown-rendered p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
    }
    
    /* Listas com texto justificado */
    li p,
    ul p,
    ol p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Blockquotes */
    blockquote p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Callouts e admonitions */
    .callout-content p,
    .admonition-content p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
}

/* ===================================================================== */
/* ELEMENTOS QUE NÃO DEVEM SER JUSTIFICADOS                             */
/* ===================================================================== */

@media print {
    /* Títulos e cabeçalhos */
    h1, h2, h3, h4, h5, h6 {
        text-align: left !important;
        hyphens: none !important;
        page-break-after: avoid !important;
        margin-top: 1.5em !important;
        margin-bottom: 0.5em !important;
    }
    
    /* Código */
    code,
    pre,
    .cm-line,
    .markdown-preview-view code,
    .markdown-preview-view pre {
        text-align: left !important;
        hyphens: none !important;
        font-family: var(--font-monospace, "Consolas", monospace) !important;
    }
    
    /* Tabelas */
    table,
    th,
    td {
        text-align: left !important;
        hyphens: none !important;
    }
    
    /* Links e elementos inline */
    a {
        text-decoration: underline !important;
        color: #0066cc !important;
    }
    
    /* Metadados e frontmatter */
    .frontmatter,
    .metadata {
        text-align: left !important;
        hyphens: none !important;
    }
}

/* ===================================================================== */
/* MELHORIAS DE TIPOGRAFIA PARA PDF                                     */
/* ===================================================================== */

@media print {
    /* Configurações de fonte otimizadas para PDF */
    body {
        font-size: 12pt !important;
        line-height: 1.6 !important;
    }
    
    /* Espaçamento entre parágrafos */
    p + p {
        margin-top: 0.5em !important;
    }
    
    /* Evitar quebras de página inadequadas */
    h1, h2, h3, h4, h5, h6 {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
    }
    
    /* Controle de quebras de página */
    p {
        page-break-inside: avoid !important;
        orphans: 2 !important;
        widows: 2 !important;
    }
    
    /* Listas */
    ul, ol {
        page-break-inside: avoid !important;
    }
    
    /* Imagens */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
    }
}

/* ===================================================================== */
/* CONFIGURAÇÕES ESPECÍFICAS PARA BETTER EXPORT PDF                     */
/* ===================================================================== */

@media print {
    /* Container principal */
    .print-container,
    .export-container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Conteúdo da página */
    .page-content {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Remover elementos desnecessários na impressão */
    .print-hide,
    .no-print,
    .obsidian-embed-toolbar,
    .file-embed-title {
        display: none !important;
    }
}

/* ===================================================================== */
/* SOLUÇÕES PARA PROBLEMAS ESPECÍFICOS DE JUSTIFICAÇÃO                  */
/* ===================================================================== */

@media print {
    /* Força justificação em todos os elementos de texto possíveis */
    div,
    span,
    section,
    article {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Elementos específicos do Obsidian que podem não estar sendo justificados */
    .markdown-embed,
    .markdown-embed-content,
    .internal-embed,
    .file-embed,
    .block-language-markdown {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Força justificação em elementos com classes específicas */
    [class*="markdown"],
    [class*="preview"],
    [class*="content"],
    [class*="text"] {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Elementos que podem ter estilos inline que impedem justificação */
    *[style*="text-align"] {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Força justificação em elementos com IDs específicos */
    [id*="content"],
    [id*="text"],
    [id*="body"] {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
}

/* ===================================================================== */
/* CONFIGURAÇÕES PARA DIFERENTES IDIOMAS                                */
/* ===================================================================== */

@media print {
    /* Configurações específicas para português */
    :lang(pt),
    :lang(pt-BR) {
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
        text-justify: inter-word !important;
    }
    
    /* Configurações para outros idiomas */
    :lang(en) {
        hyphens: auto !important;
        text-justify: inter-word !important;
    }
}

/* ===================================================================== */
/* SOLUÇÕES PARA CASOS ESPECÍFICOS PROBLEMÁTICOS                        */
/* ===================================================================== */

@media print {
    /* Força justificação usando seletores universais mais específicos */
    body * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Sobrescreve estilos inline que podem estar impedindo a justificação */
    *[style] {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Elementos que podem ter CSS específico do tema */
    .theme-light *,
    .theme-dark * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Força justificação em elementos com data-attributes */
    [data-type="markdown"],
    [data-type="text"],
    [data-mode="preview"],
    [data-mode="reading"] {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Elementos específicos que podem estar sendo ignorados */
    .workspace-leaf-content,
    .view-content,
    .markdown-source-view,
    .markdown-preview-section {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }

    /* Força justificação em todos os elementos de texto, independente da hierarquia */
    html * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
}

/* ===================================================================== */
/* RESET COMPLETO PARA CASOS EXTREMOS                                   */
/* ===================================================================== */

@media print {
    /* Reset completo - use apenas se outros métodos falharem */
    /*
    * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    */

    /* Força justificação usando !important com máxima especificidade */
    html body * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
}

/* ===================================================================== */
/* EXCEÇÕES IMPORTANTES - ELEMENTOS QUE NÃO DEVEM SER JUSTIFICADOS      */
/* ===================================================================== */

@media print {
    /* Sobrescreve as regras acima para elementos que NÃO devem ser justificados */
    h1, h1 *,
    h2, h2 *,
    h3, h3 *,
    h4, h4 *,
    h5, h5 *,
    h6, h6 * {
        text-align: left !important;
        hyphens: none !important;
    }

    /* Código e elementos técnicos */
    code, code *,
    pre, pre *,
    .cm-line, .cm-line *,
    .markdown-preview-view code, .markdown-preview-view code *,
    .markdown-preview-view pre, .markdown-preview-view pre * {
        text-align: left !important;
        hyphens: none !important;
    }

    /* Tabelas */
    table, table *,
    th, th *,
    td, td * {
        text-align: left !important;
        hyphens: none !important;
    }

    /* Metadados */
    .frontmatter, .frontmatter *,
    .metadata, .metadata * {
        text-align: left !important;
        hyphens: none !important;
    }
}

/* ===================================================================== */
/* FIM DO SNIPPET                                                       */
/* ===================================================================== */
