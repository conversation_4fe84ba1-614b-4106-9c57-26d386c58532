/* ===================================================================== */
/* CORES PERSONALIZADAS PARA BETTER EXPORT PDF                         */
/* ===================================================================== */
/* 
   Este arquivo contém diferentes variações de cor para usar com o
   snippet justificar_better_export_pdf.css
   
   COMO USAR:
   1. Escolha uma das opções abaixo
   2. Copie o código da seção escolhida
   3. Cole no arquivo justificar_better_export_pdf.css
   4. Substitua a seção "CONFIGURAÇÕES GERAIS PARA EXPORTAÇÃO PDF"
*/

/* ===================================================================== */
/* OPÇÃO 1: COR CREME (SUA COR ATUAL)                                   */
/* ===================================================================== */

@media print {
    :root {
        --pdf-background-color: rgb(253, 246, 227); /* Creme */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}

/* ===================================================================== */
/* OPÇÃO 2: BRANCO PURO                                                 */
/* ===================================================================== */

/*
@media print {
    :root {
        --pdf-background-color: rgb(255, 255, 255); /* Branco puro */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* OPÇÃO 3: OFF-WHITE (BRANCO SUAVE)                                    */
/* ===================================================================== */

/*
@media print {
    :root {
        --pdf-background-color: rgb(250, 250, 250); /* Off-white */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* OPÇÃO 4: PAPEL ANTIGO                                                */
/* ===================================================================== */

/*
@media print {
    :root {
        --pdf-background-color: rgb(255, 253, 240); /* Papel antigo */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* OPÇÃO 5: BEGE CLARO                                                  */
/* ===================================================================== */

/*
@media print {
    :root {
        --pdf-background-color: rgb(245, 245, 220); /* Bege claro */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* OPÇÃO 6: MARFIM                                                      */
/* ===================================================================== */

/*
@media print {
    :root {
        --pdf-background-color: rgb(255, 255, 240); /* Marfim */
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* OPÇÃO 7: COR PERSONALIZADA                                           */
/* ===================================================================== */

/*
@media print {
    :root {
        /* SUBSTITUA AQUI pela sua cor RGB personalizada */
        --pdf-background-color: rgb(XXX, XXX, XXX);
        --pdf-text-color: #000;
    }
    
    * { box-sizing: border-box; }
    
    body {
        margin: 0; padding: 0; line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: var(--pdf-text-color) !important;
        background: var(--pdf-background-color) !important;
    }
    
    html { background: var(--pdf-background-color) !important; }
    
    .print-container, .export-container, .page-content {
        background: var(--pdf-background-color) !important;
    }
}
*/

/* ===================================================================== */
/* INSTRUÇÕES DETALHADAS                                                */
/* ===================================================================== */

/*
COMO ALTERAR A COR:

1. MÉTODO SIMPLES:
   - Abra o arquivo justificar_better_export_pdf.css
   - Procure por: --pdf-background-color: rgb(253, 246, 227);
   - Altere os números RGB para a cor desejada

2. MÉTODO USANDO ESTE ARQUIVO:
   - Escolha uma das opções acima
   - Descomente a seção (remova /* e */)
   - Comente a seção atual (adicione /* e */)
   - Ou copie e cole no arquivo principal

3. DESCOBRIR CÓDIGOS RGB:
   - Use um seletor de cores online
   - No Obsidian, vá em Configurações > Aparência > Cores
   - Use ferramentas como ColorPicker ou similar

EXEMPLOS DE CORES POPULARES:
- Papel pergaminho: rgb(255, 248, 220)
- Creme suave: rgb(255, 253, 208)
- Branco gelo: rgb(248, 248, 255)
- Cinza muito claro: rgb(248, 248, 248)
- Amarelo muito claro: rgb(255, 255, 224)

DICA: Para manter a legibilidade, use sempre cores muito claras
com texto preto (#000) ou muito escuro.
*/
