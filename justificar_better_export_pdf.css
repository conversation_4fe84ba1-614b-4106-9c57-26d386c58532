/* ===================================================================== */
/* SNIPPET CSS PARA BETTER EXPORT PDF - TEXTO JUSTIFICADO              */
/* ===================================================================== */
/* 
   Este snippet é específico para o plugin Better Export PDF.
   
   INSTRUÇÕES DE USO:
   1. Salve este arquivo na pasta .obsidian/snippets/
   2. NÃO ative este snippet nas configurações do Obsidian
   3. No plugin Better Export PDF, ative a opção "Select CSS snippets"
   4. Durante a exportação, selecione este snippet na lista de CSS snippets
   
   IMPORTANTE: Este snippet deve ser usado APENAS com o Better Export PDF,
   não com a exportação nativa do Obsidian.
*/

/* ===================================================================== */
/* CONFIGURAÇÕES GERAIS PARA EXPORTAÇÃO PDF                             */
/* ===================================================================== */

/* Reset de margens e configurações básicas */
@media print {
    * {
        box-sizing: border-box;
    }
    
    body {
        margin: 0;
        padding: 0;
        line-height: 1.6;
        font-family: var(--font-text, "Segoe UI", system-ui, sans-serif);
        color: #000 !important;
        background: #fff !important;
    }
}

/* ===================================================================== */
/* JUSTIFICAÇÃO DE TEXTO - CONFIGURAÇÕES PRINCIPAIS                     */
/* ===================================================================== */

@media print {
    /* Parágrafos principais */
    p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
        margin-bottom: 1em !important;
        orphans: 2 !important;
        widows: 2 !important;
    }
    
    /* Conteúdo do markdown */
    .markdown-preview-view p,
    .markdown-reading-view p,
    .markdown-rendered p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
    }
    
    /* Listas com texto justificado */
    li p,
    ul p,
    ol p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Blockquotes */
    blockquote p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Callouts e admonitions */
    .callout-content p,
    .admonition-content p {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
}

/* ===================================================================== */
/* ELEMENTOS QUE NÃO DEVEM SER JUSTIFICADOS                             */
/* ===================================================================== */

@media print {
    /* Títulos e cabeçalhos */
    h1, h2, h3, h4, h5, h6 {
        text-align: left !important;
        hyphens: none !important;
        page-break-after: avoid !important;
        margin-top: 1.5em !important;
        margin-bottom: 0.5em !important;
    }
    
    /* Código */
    code,
    pre,
    .cm-line,
    .markdown-preview-view code,
    .markdown-preview-view pre {
        text-align: left !important;
        hyphens: none !important;
        font-family: var(--font-monospace, "Consolas", monospace) !important;
    }
    
    /* Tabelas */
    table,
    th,
    td {
        text-align: left !important;
        hyphens: none !important;
    }
    
    /* Links e elementos inline */
    a {
        text-decoration: underline !important;
        color: #0066cc !important;
    }
    
    /* Metadados e frontmatter */
    .frontmatter,
    .metadata {
        text-align: left !important;
        hyphens: none !important;
    }
}

/* ===================================================================== */
/* MELHORIAS DE TIPOGRAFIA PARA PDF                                     */
/* ===================================================================== */

@media print {
    /* Configurações de fonte otimizadas para PDF */
    body {
        font-size: 12pt !important;
        line-height: 1.6 !important;
    }
    
    /* Espaçamento entre parágrafos */
    p + p {
        margin-top: 0.5em !important;
    }
    
    /* Evitar quebras de página inadequadas */
    h1, h2, h3, h4, h5, h6 {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
    }
    
    /* Controle de quebras de página */
    p {
        page-break-inside: avoid !important;
        orphans: 2 !important;
        widows: 2 !important;
    }
    
    /* Listas */
    ul, ol {
        page-break-inside: avoid !important;
    }
    
    /* Imagens */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
    }
}

/* ===================================================================== */
/* CONFIGURAÇÕES ESPECÍFICAS PARA BETTER EXPORT PDF                     */
/* ===================================================================== */

@media print {
    /* Container principal */
    .print-container,
    .export-container {
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* Conteúdo da página */
    .page-content {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Remover elementos desnecessários na impressão */
    .print-hide,
    .no-print,
    .obsidian-embed-toolbar,
    .file-embed-title {
        display: none !important;
    }
}

/* ===================================================================== */
/* CONFIGURAÇÕES PARA DIFERENTES IDIOMAS                                */
/* ===================================================================== */

@media print {
    /* Configurações específicas para português */
    :lang(pt),
    :lang(pt-BR) {
        hyphens: auto !important;
        -webkit-hyphens: auto !important;
        -moz-hyphens: auto !important;
        -ms-hyphens: auto !important;
        text-justify: inter-word !important;
    }
    
    /* Configurações para outros idiomas */
    :lang(en) {
        hyphens: auto !important;
        text-justify: inter-word !important;
    }
}

/* ===================================================================== */
/* FIM DO SNIPPET                                                       */
/* ===================================================================== */
