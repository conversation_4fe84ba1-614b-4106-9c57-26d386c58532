# Como Usar o Snippet CSS para Better Export PDF

## Sobre o Snippet

O arquivo `justificar_better_export_pdf.css` foi criado especificamente para funcionar com o plugin **Better Export PDF** do Obsidian. Este snippet garante que o texto seja mantido justificado durante a exportação para PDF.

## Diferenças entre Better Export PDF e Export Nativo

### Export Nativo do Obsidian
- Usa apenas `@media print`
- Limitações de customização
- Nem sempre respeita CSS snippets complexos

### Better Export PDF Plugin
- Maior controle sobre estilos CSS
- Permite seleção de snippets específicos durante a exportação
- Melhor renderização de elementos complexos
- Suporte a margens customizadas, bookmarks e numeração de páginas

## Instruções de Instalação

### 1. Instalar o Plugin Better Export PDF

1. Vá em **Configurações** → **Plugins de Terceiros** → **Plugins da Comunidade**
2. Procure por "Better Export PDF"
3. Instale e ative o plugin

### 2. Configurar o Snippet CSS

1. **Salve o arquivo**: O arquivo `justificar_better_export_pdf.css` deve estar na pasta `.obsidian/snippets/`

2. **NÃO ative o snippet**: Diferente dos snippets normais, este NÃO deve ser ativado em **Configurações** → **Aparência** → **Snippets CSS**

3. **Configure o plugin**: 
   - Vá em **Configurações** → **Better Export PDF**
   - Ative a opção **"Select CSS snippets"**

## Como Usar

### 1. Exportar um Arquivo

1. Abra o arquivo que deseja exportar
2. Clique no menu de três pontos (⋯) no canto superior direito
3. Selecione **"Better Export PDF"**

### 2. Configurar a Exportação

1. Na janela de exportação que abrir:
   - Configure as margens conforme necessário
   - Ajuste o tamanho da página
   - Configure cabeçalho/rodapé se desejar

2. **Importante**: Procure pela seção **"CSS snippets"**
   - Marque a caixa do snippet `justificar_better_export_pdf`
   - Este snippet aparecerá apenas se você ativou "Select CSS snippets" nas configurações

### 3. Exportar

1. Clique em **"Export"**
2. Escolha o local para salvar o PDF
3. O texto será exportado com justificação completa

## Recursos do Snippet

### ✅ O que o snippet faz:

- **Justifica todo o texto**: Parágrafos, listas, blockquotes
- **Hifenização automática**: Melhora a aparência do texto justificado
- **Controle de quebras de página**: Evita quebras inadequadas
- **Tipografia otimizada**: Configurações específicas para PDF
- **Suporte multilíngue**: Configurações para português e inglês

### ❌ O que NÃO é justificado:

- Títulos e cabeçalhos (permanecem alinhados à esquerda)
- Código e blocos de código
- Tabelas
- Metadados e frontmatter

## Solução de Problemas

### O texto não está justificado no PDF:

1. **Verifique se o snippet está selecionado**: Na janela de exportação, certifique-se de que `justificar_better_export_pdf` está marcado
2. **Confirme a configuração do plugin**: A opção "Select CSS snippets" deve estar ativada
3. **Não ative o snippet globalmente**: Este snippet deve ser usado apenas durante a exportação

### O snippet não aparece na lista:

1. **Verifique o local do arquivo**: Deve estar em `.obsidian/snippets/`
2. **Reinicie o Obsidian**: Feche e abra o Obsidian novamente
3. **Verifique as configurações**: "Select CSS snippets" deve estar ativado no plugin

### Problemas de formatação:

1. **Teste com um arquivo simples**: Use um arquivo com apenas texto para testar
2. **Verifique conflitos**: Outros snippets CSS podem interferir
3. **Ajuste as margens**: Margens muito pequenas podem afetar a justificação

## Configurações Avançadas

### Personalizar o Snippet

Você pode editar o arquivo `justificar_better_export_pdf.css` para:

- Alterar o tamanho da fonte (linha com `font-size: 12pt`)
- Modificar o espaçamento entre linhas (`line-height: 1.6`)
- Ajustar o espaçamento entre parágrafos
- Personalizar a família de fontes

### Exemplo de Personalização

```css
@media print {
    body {
        font-size: 14pt !important;  /* Fonte maior */
        line-height: 1.8 !important; /* Mais espaçamento */
        font-family: "Times New Roman" !important; /* Fonte específica */
    }
}
```

## Dicas Adicionais

1. **Use margens adequadas**: Margens muito pequenas podem prejudicar a leitura
2. **Teste diferentes configurações**: Experimente diferentes tamanhos de página
3. **Combine com outros recursos**: Use bookmarks e numeração de páginas
4. **Mantenha backups**: Sempre mantenha uma cópia do snippet original

## Suporte

Se encontrar problemas:

1. Verifique a documentação oficial do Better Export PDF
2. Consulte os fóruns da comunidade Obsidian
3. Teste com arquivos simples primeiro
4. Verifique se há atualizações do plugin

---

**Nota**: Este snippet foi otimizado para o plugin Better Export PDF versão 1.11.0 ou superior. Versões anteriores podem ter comportamentos diferentes.
