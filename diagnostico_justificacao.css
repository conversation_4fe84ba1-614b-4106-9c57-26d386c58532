/* ===================================================================== */
/* SNIPPET DE DIAGNÓSTICO PARA PROBLEMAS DE JUSTIFICAÇÃO                */
/* ===================================================================== */
/* 
   Este snippet é para DIAGNÓSTICO APENAS.
   Use-o temporariamente para identificar problemas específicos.
   
   INSTRUÇÕES:
   1. Ative este snippet temporariamente nas configurações do Obsidian
   2. Observe as cores que aparecem no texto
   3. Identifique quais elementos não estão sendo justificados
   4. Desative este snippet após o diagnóstico
   5. Use as informações para ajustar o snippet principal
*/

/* ===================================================================== */
/* CORES DE DIAGNÓSTICO - APENAS PARA VISUALIZAÇÃO NO OBSIDIAN          */
/* ===================================================================== */

/* Elementos que DEVEM ser justificados - cor verde */
.markdown-preview-view p,
.markdown-reading-view p,
.markdown-rendered p {
    background-color: rgba(0, 255, 0, 0.1) !important;
    border-left: 3px solid green !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Listas - cor azul */
li p,
ul p,
ol p {
    background-color: rgba(0, 0, 255, 0.1) !important;
    border-left: 3px solid blue !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Blockquotes - cor roxa */
blockquote p {
    background-color: rgba(128, 0, 128, 0.1) !important;
    border-left: 3px solid purple !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Callouts - cor laranja */
.callout-content p,
.admonition-content p {
    background-color: rgba(255, 165, 0, 0.1) !important;
    border-left: 3px solid orange !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Elementos que podem estar causando problemas - cor vermelha */
div,
span,
section,
article {
    background-color: rgba(255, 0, 0, 0.05) !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Elementos específicos do Obsidian - cor amarela */
.markdown-embed,
.markdown-embed-content,
.internal-embed,
.file-embed {
    background-color: rgba(255, 255, 0, 0.1) !important;
    border: 1px dashed yellow !important;
    text-align: justify !important;
    text-justify: inter-word !important;
}

/* Elementos que NÃO devem ser justificados - cor cinza */
h1, h2, h3, h4, h5, h6 {
    background-color: rgba(128, 128, 128, 0.1) !important;
    border-left: 3px solid gray !important;
    text-align: left !important;
}

code,
pre {
    background-color: rgba(128, 128, 128, 0.2) !important;
    border: 1px solid gray !important;
    text-align: left !important;
}

/* ===================================================================== */
/* DIAGNÓSTICO PARA EXPORTAÇÃO PDF                                      */
/* ===================================================================== */

@media print {
    /* Marca elementos que devem ser justificados com bordas */
    p {
        border-left: 5px solid green !important;
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Marca elementos problemáticos */
    div,
    span {
        border-left: 2px solid red !important;
        text-align: justify !important;
        text-justify: inter-word !important;
    }
    
    /* Força justificação em TODOS os elementos para teste */
    * {
        text-align: justify !important;
        text-justify: inter-word !important;
        hyphens: auto !important;
    }
    
    /* Exceções importantes */
    h1, h2, h3, h4, h5, h6,
    code, pre,
    table, th, td {
        text-align: left !important;
        hyphens: none !important;
        border-left: 5px solid gray !important;
    }
}

/* ===================================================================== */
/* INFORMAÇÕES DE DEBUG                                                 */
/* ===================================================================== */

/* Adiciona informações visuais sobre os elementos */
.markdown-preview-view p::before {
    content: "[P] ";
    color: green;
    font-weight: bold;
    font-size: 10px;
}

.markdown-preview-view div::before {
    content: "[DIV] ";
    color: red;
    font-weight: bold;
    font-size: 8px;
}

.markdown-embed::before {
    content: "[EMBED] ";
    color: orange;
    font-weight: bold;
    font-size: 8px;
}

/* ===================================================================== */
/* INSTRUÇÕES DE USO                                                    */
/* ===================================================================== */

/*
COMO USAR ESTE SNIPPET DE DIAGNÓSTICO:

1. ATIVE este snippet nas configurações do Obsidian
2. Abra a nota que está com problemas de justificação
3. Observe as cores e bordas que aparecem:
   - Verde: Elementos que devem ser justificados
   - Azul: Listas
   - Roxo: Blockquotes
   - Laranja: Callouts
   - Amarelo: Embeds
   - Vermelho: Elementos genéricos (podem causar problemas)
   - Cinza: Elementos que NÃO devem ser justificados

4. Exporte para PDF usando Better Export PDF
5. Verifique se as bordas aparecem no PDF
6. Identifique quais elementos não estão justificados
7. DESATIVE este snippet após o diagnóstico

INTERPRETAÇÃO DOS RESULTADOS:

- Se um parágrafo aparece com borda verde mas não está justificado no PDF,
  o problema pode ser com o seletor CSS específico
  
- Se elementos aparecem com borda vermelha e não estão justificados,
  eles podem estar sobrescrevendo as regras de justificação
  
- Se não há bordas visíveis, o CSS não está sendo aplicado

PRÓXIMOS PASSOS:

Com base no que você observar, podemos ajustar o snippet principal
para resolver os problemas específicos da sua nota.
*/
